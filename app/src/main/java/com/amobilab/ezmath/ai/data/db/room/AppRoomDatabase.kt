package com.amobilab.ezmath.ai.data.db.room

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

@Database(
    entities = [
        HistoryEntity::class,
        ChatEntity::class,
        CoinHistoryEntity::class
    ],
    version = 3,
    exportSchema = true
)
abstract class AppRoomDatabase : RoomDatabase() {
    
    abstract fun historyDao(): HistoryDao
    abstract fun chatDao(): ChatDao
    abstract fun coinHistoryDao(): CoinHistoryDao
    
    fun closeDatabase() {
        if (isOpen) {
            close()
        }
        INSTANCE = null
    }

    companion object {
        @Volatile
        private var INSTANCE: AppRoomDatabase? = null

        const val DATABASE_NAME = "app_database"

        fun getInstance(context: Context): AppRoomDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppRoomDatabase::class.java,
                    DATABASE_NAME
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
