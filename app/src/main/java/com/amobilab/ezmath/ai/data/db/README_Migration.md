# Migration từ Room sang PowerSync - Hướng dẫn đầy đủ

## 📋 Tổng quan

Hệ thống migration này được thiết kế để tự động chuyển đổi dữ liệu từ Room database sang PowerSync database khi người dùng cập nhật ứng dụng. Migration chỉ chạy một lần duy nhất và đảm bảo an toàn dữ liệu.

## 🏗️ Kiến trúc

### Files chính:
- `AppDatabase.kt` - Chứa logic migration chính
- `AppRoomDatabase.kt` - Room database class để đọc dữ liệu cũ
- `MigrationGuide.md` - Hướng dẫn chi tiết
- `MigrationExample.kt` - Ví dụ sử dụng
- `MigrationTest.kt` - Testing utilities

### Luồng hoạt động:
```
App Update → init() → performAutoMigrationIfNeeded() → migrateRoomToPowerSync()
    ↓
Check Room DB exists → Read Room data → Convert to PowerSync → Insert to PowerSync
    ↓
Clean up Room DB → Mark migration completed
```

## 🚀 Cách sử dụng

### 1. Tự động Migration (Khuyến nghị)

```kotlin
// Trong Application class
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        GlobalScope.launch {
            AppDatabase.getInstance().init(this@MyApplication)
        }
    }
}
```

### 2. Trong Activity với Lifecycle

```kotlin
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        lifecycleScope.launch {
            try {
                AppDatabase.getInstance().init(this@MainActivity)
                // Database ready to use
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
}
```

### 3. Migration thủ công

```kotlin
lifecycleScope.launch {
    val success = AppDatabase.getInstance().forceMigration(context)
    if (success) {
        // Migration successful
    }
}
```

## 📊 Mapping dữ liệu

### HistoryEntity
| Room | PowerSync | Ghi chú |
|------|-----------|---------|
| historyId: Int | id: String | UUID mới |
| timestamp: Long | timestamp: Long | Giữ nguyên |
| historyName: String | historyName: String | Giữ nguyên |
| isFavorite: Boolean | isFavorite: Boolean | Giữ nguyên |
| content: String | content: String | Giữ nguyên |
| imageData: ByteArray? | imageData: ByteArray? | Giữ nguyên |
| questionMode: String | questionMode: String | Giữ nguyên |
| modelAiChat: String | modelAiChat: String | Giữ nguyên |

### ChatEntity
| Room | PowerSync | Ghi chú |
|------|-----------|---------|
| chatListId: Int | id: String | UUID mới |
| historyId: Long | historyId: String | Mapped từ Room historyId |
| timestamp: Long | timestamp: Long | Giữ nguyên |
| content: String | content: String | Giữ nguyên |
| isHuman: Boolean | isHuman: Boolean | Giữ nguyên |
| isError: Boolean | isError: Boolean | Giữ nguyên |
| imageData: ByteArray? | imageData: ByteArray? | Giữ nguyên |
| botName: String? | botName: String? | Giữ nguyên |

### CoinHistoryEntity
| Room | PowerSync | Ghi chú |
|------|-----------|---------|
| id: Long | id: String | UUID mới |
| type: TransactionType | type: TransactionType | Enum mapping |
| amount: Int | amount: Int | Giữ nguyên |
| date: Long | date: Long | Giữ nguyên |
| description: String | description: String | Giữ nguyên |

## 🔧 API Reference

### AppDatabase Methods

```kotlin
// Khởi tạo database với auto migration
suspend fun init(context: Context)

// Migration thủ công
suspend fun forceMigration(context: Context): Boolean

// Reset migration status (testing only)
fun resetMigrationStatus()

// Kiểm tra migration status
private fun isMigrationCompleted(): Boolean
```

### DatabaseUtils Methods

```kotlin
// Kiểm tra migration đã hoàn thành
fun isMigrationCompleted(): Boolean

// Reset cho testing
fun resetMigrationForTesting()

// Khởi tạo an toàn
suspend fun safeInitializeDatabase(context: Context): Boolean
```

## 🧪 Testing

### Chạy test migration:

```kotlin
// Test cơ bản
context.testMigration()

// Test chi tiết
val migrationTest = MigrationTest()
val result = migrationTest.testRealMigration(context)

// Test performance
val perfResult = migrationTest.testMigrationPerformance(context)
```

### Test scenarios:
1. **Real Migration Test** - Test với dữ liệu thực
2. **Performance Test** - Đo thời gian migration
3. **Rollback Test** - Kiểm tra Room DB còn lại khi thất bại
4. **Mock Migration Test** - Test với dữ liệu mock

## ⚠️ Lưu ý quan trọng

### An toàn dữ liệu:
- ✅ Room database chỉ bị xóa sau khi PowerSync insert thành công
- ✅ Nếu migration thất bại, Room database được giữ lại
- ✅ Migration chỉ chạy một lần duy nhất
- ✅ Có logging chi tiết cho debugging

### Performance:
- ⏱️ Migration có thể mất thời gian với dữ liệu lớn
- 🔄 Chạy trong background thread (Dispatchers.IO)
- 📊 Có thể monitor progress qua logs

### Error Handling:
- 🛡️ Try-catch toàn bộ quá trình migration
- 📝 Log chi tiết mọi bước
- 🔄 Có thể retry migration nếu thất bại

## 🐛 Troubleshooting

### Migration không chạy:
```kotlin
// Kiểm tra init() có được gọi đúng không
AppDatabase.getInstance().init(context) // Phải là suspend function

// Kiểm tra migration status
val completed = DatabaseUtils.isMigrationCompleted()
```

### Migration thất bại:
```kotlin
// Xem logs để debug
debugLog("migrateRoomToPowerSync: ...")

// Reset và thử lại
AppDatabase.getInstance().resetMigrationStatus()
AppDatabase.getInstance().forceMigration(context)
```

### Dữ liệu không đúng:
```kotlin
// Verify dữ liệu sau migration
val histories = AppDatabase.getInstance().getHistoryRepository().getAllHistory()
val chats = AppDatabase.getInstance().getChatRepository().getChatsForHistory(historyId)
```

## 📈 Monitoring

### Logs quan trọng:
- `migrateRoomToPowerSync: Bắt đầu migration`
- `migrateRoomToPowerSync: Tìm thấy X histories và Y coin histories`
- `migrateRoomToPowerSync: Migration hoàn thành thành công`
- `migrateRoomToPowerSync: Lỗi trong quá trình migration`

### Metrics:
- Số lượng histories migrated
- Số lượng chats migrated
- Số lượng coin histories migrated
- Thời gian migration
- Success/failure rate

## 🔄 Rollback Plan

Nếu cần rollback:
1. Stop app
2. Restore Room database từ backup (nếu có)
3. Reset migration status: `resetMigrationStatus()`
4. Restart app với Room database

## 📝 Changelog

### Version 1.0
- ✅ Basic migration từ Room sang PowerSync
- ✅ Auto migration khi init database
- ✅ Manual migration support
- ✅ Testing utilities
- ✅ Error handling và logging
- ✅ Data mapping cho tất cả entities
- ✅ Safe cleanup Room database
