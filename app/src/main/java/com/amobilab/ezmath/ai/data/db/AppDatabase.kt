package com.amobilab.ezmath.ai.data.db

import android.content.Context
import com.amobilab.ezmath.ai.data.db.powerSync.AppSchema
import com.powersync.PowerSyncDatabase
import com.powersync.db.schema.Column
import com.powersync.db.schema.Index
import com.powersync.db.schema.IndexedColumn
import com.powersync.db.schema.Schema
import com.powersync.db.schema.Table
import com.powersync.db.schema.ColumnType
import com.amobilab.ezmath.ai.data.db.powerSync.ChatRepository
import com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryRepository
import com.amobilab.ezmath.ai.data.db.powerSync.HistoryRepository
import com.amobilab.ezmath.ai.data.db.powerSync.MyConnector
import com.amobilab.ezmath.ai.data.db.room.AppRoomDatabase
import com.powersync.DatabaseDriverFactory
import amobi.module.common.utils.debugLog
import amobi.module.common.configs.PrefAssist
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.UUID

class AppDatabase private constructor() {

    companion object {
        @JvmStatic
        @Synchronized
        fun getInstance() = Holder.instance

        const val DB_NAME = "app_db"
        private const val PREF_MIGRATION_COMPLETED = "room_to_powersync_migration_completed"
    }

    private object Holder {
        val instance = AppDatabase()
    }

    private lateinit var db: PowerSyncDatabase
    private lateinit var historyRepository: HistoryRepository
    private lateinit var chatRepository: ChatRepository
    private lateinit var coinHistoryRepository: CoinHistoryRepository

    suspend fun init(context: Context) {
        if (!::db.isInitialized) {
            // Khởi tạo PowerSyncDatabase
            val driverFactory = DatabaseDriverFactory(context)
            val dbPath = File(context.filesDir, DB_NAME)
            db = PowerSyncDatabase(
                factory = driverFactory,
                schema = AppSchema,
                dbFilename = dbPath.absolutePath
            )

            // Khởi tạo các repository
            historyRepository = HistoryRepository(db)
            chatRepository = ChatRepository(db)
            coinHistoryRepository = CoinHistoryRepository(db)

            // Thực hiện auto migration nếu cần
            performAutoMigrationIfNeeded(context)
        }
    }

    fun getHistoryRepository(): HistoryRepository {
        check(::historyRepository.isInitialized) { "Database not initialized. Call init() first." }
        return historyRepository
    }

    fun getChatRepository(): ChatRepository {
        check(::chatRepository.isInitialized) { "Database not initialized. Call init() first." }
        return chatRepository
    }

    fun getCoinHistoryRepository(): CoinHistoryRepository {
        check(::coinHistoryRepository.isInitialized) { "Database not initialized. Call init() first." }
        return coinHistoryRepository
    }

    // Phương thức để kết nối với backend
    suspend fun connectToBackend(/* backend configuration */) {
         db.connect(MyConnector())
    }

    // Phương thức để kết nối với backend
    suspend fun disconnectAndClearForLogout(/* backend configuration */) {
        db.disconnectAndClear()
    }

    /**
     * Hàm migration từ Room database sang PowerSync database
     * Được gọi khi người dùng cập nhật app để tự động chuyển đổi dữ liệu
     */
    suspend fun migrateRoomToPowerSync(context: Context): Boolean = withContext(Dispatchers.IO) {
        try {
            debugLog("migrateRoomToPowerSync: Bắt đầu migration từ Room sang PowerSync")

            // Kiểm tra xem có Room database cũ không
            val roomDbFile = context.getDatabasePath(AppRoomDatabase.DATABASE_NAME)
            if (!roomDbFile.exists()) {
                debugLog("migrateRoomToPowerSync: Không tìm thấy Room database cũ, bỏ qua migration")
                return@withContext true
            }

            // Khởi tạo Room database để đọc dữ liệu cũ
            val roomDb = AppRoomDatabase.getInstance(context)

            debugLog("migrateRoomToPowerSync: Đang đọc dữ liệu từ Room database...")

            // Đọc tất cả dữ liệu từ Room
            val roomHistories = roomDb.historyDao().getAllHistory()
            val roomCoinHistories = roomDb.coinHistoryDao().getAllTransactions()

            debugLog("migrateRoomToPowerSync: Tìm thấy ${roomHistories.size} histories và ${roomCoinHistories.size} coin histories")

            // Map để lưu trữ mapping giữa Room historyId và PowerSync historyId
            val historyIdMapping = mutableMapOf<Int, String>()

            // Migration History data
            debugLog("migrateRoomToPowerSync: Đang migrate History data...")
            for (roomHistory in roomHistories) {
                val powerSyncHistoryId = UUID.randomUUID().toString()
                historyIdMapping[roomHistory.historyId] = powerSyncHistoryId

                val powerSyncHistory = com.amobilab.ezmath.ai.data.db.powerSync.HistoryEntity(
                    id = powerSyncHistoryId,
                    timestamp = roomHistory.timestamp,
                    historyName = roomHistory.historyName,
                    isFavorite = roomHistory.isFavorite,
                    content = roomHistory.content,
                    imageData = roomHistory.imageData,
                    questionMode = roomHistory.questionMode,
                    modelAiChat = roomHistory.modelAiChat
                )

                historyRepository.insertHistory(powerSyncHistory)
            }

            // Migration Chat data
            debugLog("migrateRoomToPowerSync: Đang migrate Chat data...")
            for ((roomHistoryId, powerSyncHistoryId) in historyIdMapping) {
                val roomChats = roomDb.chatDao().getChatsForHistory(roomHistoryId.toLong())
                debugLog("migrateRoomToPowerSync: Tìm thấy ${roomChats.size} chats cho history $roomHistoryId")

                for (roomChat in roomChats) {
                    val powerSyncChat = com.amobilab.ezmath.ai.data.db.powerSync.ChatEntity(
                        id = UUID.randomUUID().toString(),
                        historyId = powerSyncHistoryId,
                        timestamp = roomChat.timestamp,
                        content = roomChat.content,
                        isHuman = roomChat.isHuman,
                        isError = roomChat.isError,
                        imageData = roomChat.imageData,
                        botName = roomChat.botName
                    )

                    chatRepository.insertChat(powerSyncChat)
                }
            }

            // Migration CoinHistory data
            debugLog("migrateRoomToPowerSync: Đang migrate CoinHistory data...")
            for (roomCoinHistory in roomCoinHistories) {
                val powerSyncCoinHistory = com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryEntity(
                    id = UUID.randomUUID().toString(),
                    type = when (roomCoinHistory.type) {
                        com.amobilab.ezmath.ai.data.db.room.TransactionType.EARN ->
                            com.amobilab.ezmath.ai.data.db.powerSync.TransactionType.EARN
                        com.amobilab.ezmath.ai.data.db.room.TransactionType.SPEND ->
                            com.amobilab.ezmath.ai.data.db.powerSync.TransactionType.SPEND
                    },
                    amount = roomCoinHistory.amount,
                    date = roomCoinHistory.date,
                    description = roomCoinHistory.description
                )

                coinHistoryRepository.insertTransaction(powerSyncCoinHistory)
            }

            debugLog("migrateRoomToPowerSync: Migration hoàn thành thành công")

            // Xóa Room database cũ sau khi migration thành công
            try {
                roomDb.closeDatabase()
                if (roomDbFile.delete()) {
                    debugLog("migrateRoomToPowerSync: Đã xóa Room database cũ")
                } else {
                    debugLog("migrateRoomToPowerSync: Không thể xóa Room database cũ")
                }
            } catch (e: Exception) {
                debugLog("migrateRoomToPowerSync: Lỗi khi xóa Room database: ${e.message}")
            }

            true
        } catch (e: Exception) {
            debugLog("migrateRoomToPowerSync: Lỗi trong quá trình migration: ${e.message}")
            e.printStackTrace()
            false
        }
    }

    /**
     * Kiểm tra xem migration đã được thực hiện chưa
     */
    private fun isMigrationCompleted(): Boolean {
        return PrefAssist.getBoolean(PREF_MIGRATION_COMPLETED, false)
    }

    /**
     * Đánh dấu migration đã hoàn thành
     */
    private fun markMigrationCompleted() {
        PrefAssist.putBoolean(PREF_MIGRATION_COMPLETED, true)
    }

    /**
     * Thực hiện migration tự động khi khởi tạo database
     * Nên được gọi trong init() method
     */
    suspend fun performAutoMigrationIfNeeded(context: Context) {
        if (!isMigrationCompleted()) {
            debugLog("performAutoMigrationIfNeeded: Bắt đầu auto migration")
            val success = migrateRoomToPowerSync(context)
            if (success) {
                markMigrationCompleted()
                debugLog("performAutoMigrationIfNeeded: Auto migration hoàn thành thành công")
            } else {
                debugLog("performAutoMigrationIfNeeded: Auto migration thất bại")
            }
        } else {
            debugLog("performAutoMigrationIfNeeded: Migration đã được thực hiện trước đó")
        }
    }

    /**
     * Hàm tiện ích để thực hiện migration thủ công
     * Có thể được gọi từ UI hoặc settings
     */
    suspend fun forceMigration(context: Context): Boolean {
        debugLog("forceMigration: Thực hiện migration thủ công")
        val success = migrateRoomToPowerSync(context)
        if (success) {
            markMigrationCompleted()
        }
        return success
    }

    /**
     * Reset trạng thái migration (chỉ dùng cho testing)
     */
    fun resetMigrationStatus() {
        PrefAssist.putBoolean(PREF_MIGRATION_COMPLETED, false)
        debugLog("resetMigrationStatus: Đã reset trạng thái migration")
    }
}