package com.amobilab.ezmath.ai.utils

import amobi.module.common.configs.CommFigs
import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import kotlin.math.max

object BitmapUtils {

    fun getBitmapFromByteArray(input: ByteArray?): Bitmap {
        return BitmapFactory.decodeByteArray(input, 0, input!!.size)
    }

    fun getBitmapFromUriOrFile(contentResolver: ContentResolver, uriString: String): ImageBitmap? {
        return try {
            val uri = Uri.parse(uriString)

            if (uriString.startsWith("file:/")) {
                // Xử lý đường dẫn tệp
                val file = File(uriString.replace("file:/", ""))
                val bitmap = BitmapFactory.decodeFile(file.absolutePath)
                bitmap.asImageBitmap()
            } else {
                // Xử lý URI nội dung
                val inputStream: InputStream? = contentResolver.openInputStream(uri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                bitmap?.asImageBitmap()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun resizeImageBitmapToStandardSize(bitmap: Bitmap): Bitmap {
        val targetWidth = if (!CommFigs.IS_WEAK_DEVICE) 1440 else 1440 / 2
        val targetHeight = if (!CommFigs.IS_WEAK_DEVICE) 810 else 810 / 2

        // Kiểm tra kích thước bitmap gốc
        if (bitmap.width > targetWidth || bitmap.height > targetHeight) {
            val scaleWidth = targetWidth.toFloat() / bitmap.width
            val scaleHeight = targetHeight.toFloat() / bitmap.height
            val scaleFinal = max(scaleWidth, scaleHeight)
            return Bitmap.createScaledBitmap(
                bitmap,
                (bitmap.width * scaleFinal).toInt(),
                (bitmap.height * scaleFinal).toInt(),
                !CommFigs.IS_WEAK_DEVICE
            )
        }
        return bitmap
    }

    fun saveBitmapToCache(context: Context, bitmap: Bitmap, fileName: String): String {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, fileName)

        return try {
            FileOutputStream(file).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
            }
            // Trả về đường dẫn URL của file
            file.toURI().toString()
        } catch (e: IOException) {
            e.printStackTrace()
            ""
        }
    }

    fun getByteArrayFromBitmap(bitmap: Bitmap): ByteArray {
        val stream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, stream)
        return stream.toByteArray()
    }

}