package com.amobilab.ezmath.ai.data.db

import android.content.Context
import amobi.module.common.utils.debugLog
import kotlinx.coroutines.runBlocking

/**
 * Class để test Migration từ Room sang PowerSync
 * Chỉ dùng cho development và testing
 */
class MigrationTest {
    
    /**
     * Test migration với dữ liệu thực
     */
    suspend fun testRealMigration(context: Context): TestResult {
        return try {
            debugLog("MigrationTest: Bắt đầu test migration thực")
            
            // Kiểm tra trạng thái migration hiện tại
            val wasCompleted = DatabaseUtils.isMigrationCompleted()
            debugLog("MigrationTest: Migration đã hoàn thành trước đó: $wasCompleted")
            
            // Reset để test lại
            AppDatabase.getInstance().resetMigrationStatus()
            
            // Thực hiện migration
            val success = AppDatabase.getInstance().forceMigration(context)
            
            if (success) {
                // Verify dữ liệu
                val verifyResult = verifyMigratedData()
                TestResult.Success("Migration thành công. $verifyResult")
            } else {
                TestResult.Failure("Migration thất bại")
            }
            
        } catch (e: Exception) {
            debugLog("MigrationTest: Lỗi trong test: ${e.message}")
            TestResult.Error(e.message ?: "Unknown error")
        }
    }
    
    /**
     * Test migration với dữ liệu mock
     */
    suspend fun testMockMigration(context: Context): TestResult {
        return try {
            debugLog("MigrationTest: Bắt đầu test migration với mock data")
            
            // Tạo mock Room database (nếu cần)
            // createMockRoomData(context)
            
            // Reset migration status
            AppDatabase.getInstance().resetMigrationStatus()
            
            // Thực hiện migration
            val success = AppDatabase.getInstance().forceMigration(context)
            
            if (success) {
                TestResult.Success("Mock migration thành công")
            } else {
                TestResult.Failure("Mock migration thất bại")
            }
            
        } catch (e: Exception) {
            TestResult.Error(e.message ?: "Unknown error")
        }
    }
    
    /**
     * Verify dữ liệu sau migration
     */
    private suspend fun verifyMigratedData(): String {
        return try {
            val historyRepo = AppDatabase.getInstance().getHistoryRepository()
            val chatRepo = AppDatabase.getInstance().getChatRepository()
            val coinRepo = AppDatabase.getInstance().getCoinHistoryRepository()
            
            val histories = historyRepo.getAllHistory()
            val coinHistories = coinRepo.getAllTransactions()
            
            // Đếm tổng số chat
            var totalChats = 0
            for (history in histories) {
                val chats = chatRepo.getChatsForHistory(history.id)
                totalChats += chats.size
            }
            
            "Verified: ${histories.size} histories, $totalChats chats, ${coinHistories.size} coin histories"
            
        } catch (e: Exception) {
            "Verification failed: ${e.message}"
        }
    }
    
    /**
     * Test performance của migration
     */
    suspend fun testMigrationPerformance(context: Context): PerformanceResult {
        val startTime = System.currentTimeMillis()
        
        return try {
            // Reset migration
            AppDatabase.getInstance().resetMigrationStatus()
            
            // Thực hiện migration
            val success = AppDatabase.getInstance().forceMigration(context)
            
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            
            if (success) {
                val dataInfo = verifyMigratedData()
                PerformanceResult.Success(duration, dataInfo)
            } else {
                PerformanceResult.Failure(duration, "Migration failed")
            }
            
        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            PerformanceResult.Error(duration, e.message ?: "Unknown error")
        }
    }
    
    /**
     * Test rollback scenario (nếu migration thất bại)
     */
    suspend fun testRollbackScenario(context: Context): TestResult {
        return try {
            debugLog("MigrationTest: Test rollback scenario")
            
            // Kiểm tra Room database vẫn còn sau migration thất bại
            val roomDbFile = context.getDatabasePath(com.amobilab.ezmath.ai.data.db.room.AppRoomDatabase.DATABASE_NAME)
            
            if (roomDbFile.exists()) {
                // Thử đọc dữ liệu từ Room database
                val roomDb = com.amobilab.ezmath.ai.data.db.room.AppRoomDatabase.getInstance(context)
                val histories = roomDb.historyDao().getAllHistory()
                
                TestResult.Success("Rollback OK: Room database còn ${histories.size} histories")
            } else {
                TestResult.Failure("Rollback failed: Room database đã bị xóa")
            }
            
        } catch (e: Exception) {
            TestResult.Error("Rollback test error: ${e.message}")
        }
    }
    
    /**
     * Chạy tất cả các test
     */
    suspend fun runAllTests(context: Context): List<Pair<String, TestResult>> {
        val results = mutableListOf<Pair<String, TestResult>>()
        
        results.add("Real Migration" to testRealMigration(context))
        results.add("Performance Test" to testMigrationPerformance(context).toTestResult())
        results.add("Rollback Test" to testRollbackScenario(context))
        
        return results
    }
    
    sealed class TestResult {
        data class Success(val message: String) : TestResult()
        data class Failure(val message: String) : TestResult()
        data class Error(val message: String) : TestResult()
    }
    
    sealed class PerformanceResult {
        data class Success(val durationMs: Long, val dataInfo: String) : PerformanceResult()
        data class Failure(val durationMs: Long, val message: String) : PerformanceResult()
        data class Error(val durationMs: Long, val message: String) : PerformanceResult()
        
        fun toTestResult(): TestResult {
            return when (this) {
                is Success -> TestResult.Success("Performance: ${durationMs}ms, $dataInfo")
                is Failure -> TestResult.Failure("Performance: ${durationMs}ms, $message")
                is Error -> TestResult.Error("Performance: ${durationMs}ms, $message")
            }
        }
    }
}

/**
 * Utility class cho migration testing
 */
object DatabaseUtils {
    
    fun isMigrationCompleted(): Boolean {
        return amobi.module.common.configs.PrefAssist.getBoolean(
            "room_to_powersync_migration_completed", 
            false
        )
    }
    
    fun resetMigrationForTesting() {
        AppDatabase.getInstance().resetMigrationStatus()
        debugLog("DatabaseUtils: Migration status reset for testing")
    }
    
    suspend fun safeInitializeDatabase(context: Context): Boolean {
        return try {
            AppDatabase.getInstance().init(context)
            true
        } catch (e: Exception) {
            debugLog("DatabaseUtils: Safe initialization failed: ${e.message}")
            false
        }
    }
}

/**
 * Extension function để chạy migration test dễ dàng
 */
fun Context.testMigration() {
    runBlocking {
        val migrationTest = MigrationTest()
        val results = migrationTest.runAllTests(this@testMigration)
        
        debugLog("=== Migration Test Results ===")
        results.forEach { (testName, result) ->
            when (result) {
                is MigrationTest.TestResult.Success -> 
                    debugLog("✅ $testName: ${result.message}")
                is MigrationTest.TestResult.Failure -> 
                    debugLog("❌ $testName: ${result.message}")
                is MigrationTest.TestResult.Error -> 
                    debugLog("🔥 $testName: ${result.message}")
            }
        }
        debugLog("=== End Migration Test Results ===")
    }
}
