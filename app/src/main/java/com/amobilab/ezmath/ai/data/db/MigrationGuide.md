# Hướng dẫn Migration từ Room sang PowerSync

## Tổng quan

Hàm `migrateRoomToPowerSync` được tạo để tự động chuyển đổi dữ liệu từ Room database sang PowerSync database khi người dùng cập nhật ứng dụng.

## Cách hoạt động

### 1. Tự động Migration
- Migration sẽ tự động chạy khi khởi tạo `AppDatabase` lần đầu tiên sau khi cập nhật app
- Hệ thống sử dụng SharedPreferences để theo dõi trạng thái migration
- Chỉ chạy một lần duy nhất cho mỗi thiết bị

### 2. Quy trình Migration

#### Bước 1: Kiểm tra Room Database
- Kiểm tra xem có file Room database cũ (`app_database`) không
- Nếu không có, bỏ qua migration

#### Bước 2: Đọ<PERSON> dữ liệu từ Room
- Đ<PERSON><PERSON> tất cả `HistoryEntity` từ Room
- Đ<PERSON><PERSON> tất cả `CoinHistoryEntity` từ Room
- Tạo mapping giữa Room `historyId` (Int) và PowerSync `historyId` (String)

#### Bước 3: Chuyển đổi và chèn dữ liệu
- **History**: Chuyển đổi từ Room HistoryEntity sang PowerSync HistoryEntity
- **Chat**: Đọc chat theo từng history và chuyển đổi
- **CoinHistory**: Chuyển đổi trực tiếp

#### Bước 4: Dọn dẹp
- Đóng Room database
- Xóa file Room database cũ
- Đánh dấu migration đã hoàn thành

### 3. Mapping dữ liệu

#### HistoryEntity
```kotlin
Room HistoryEntity -> PowerSync HistoryEntity
- historyId: Int -> id: String (UUID)
- timestamp: Long -> timestamp: Long
- historyName: String -> historyName: String
- isFavorite: Boolean -> isFavorite: Boolean
- content: String -> content: String
- imageData: ByteArray? -> imageData: ByteArray?
- questionMode: String -> questionMode: String
- modelAiChat: String -> modelAiChat: String
```

#### ChatEntity
```kotlin
Room ChatEntity -> PowerSync ChatEntity
- chatListId: Int -> id: String (UUID)
- historyId: Long -> historyId: String (mapped từ Room historyId)
- timestamp: Long -> timestamp: Long
- content: String -> content: String
- isHuman: Boolean -> isHuman: Boolean
- isError: Boolean -> isError: Boolean
- imageData: ByteArray? -> imageData: ByteArray?
- botName: String? -> botName: String?
```

#### CoinHistoryEntity
```kotlin
Room CoinHistoryEntity -> PowerSync CoinHistoryEntity
- id: Long -> id: String (UUID)
- type: TransactionType -> type: TransactionType
- amount: Int -> amount: Int
- date: Long -> date: Long
- description: String -> description: String
```

## Cách sử dụng

### 1. Tự động (Khuyến nghị)
Migration sẽ tự động chạy khi gọi `AppDatabase.getInstance().init(context)`:

```kotlin
// Trong Application class hoặc MainActivity
lifecycleScope.launch {
    AppDatabase.getInstance().init(context)
}
```

### 2. Thủ công
Nếu cần chạy migration thủ công:

```kotlin
lifecycleScope.launch {
    val success = AppDatabase.getInstance().forceMigration(context)
    if (success) {
        // Migration thành công
    } else {
        // Migration thất bại
    }
}
```

### 3. Reset Migration (Chỉ dùng cho testing)
```kotlin
AppDatabase.getInstance().resetMigrationStatus()
```

## Lưu ý quan trọng

1. **Backup dữ liệu**: Migration sẽ xóa Room database sau khi hoàn thành. Đảm bảo PowerSync hoạt động tốt trước khi triển khai.

2. **Error Handling**: Nếu migration thất bại, Room database sẽ không bị xóa và có thể thử lại.

3. **Performance**: Migration có thể mất thời gian với dữ liệu lớn. Nên chạy trong background thread.

4. **Logging**: Tất cả các bước migration đều được log để debug.

## Troubleshooting

### Migration không chạy
- Kiểm tra xem `init()` có được gọi với `suspend` không
- Kiểm tra log để xem migration status

### Migration thất bại
- Kiểm tra log để xem lỗi cụ thể
- Đảm bảo PowerSync database đã được khởi tạo đúng cách
- Kiểm tra quyền truy cập file

### Dữ liệu bị mất
- Migration chỉ xóa Room database sau khi chèn thành công vào PowerSync
- Nếu có lỗi, Room database sẽ được giữ lại

## Testing

Để test migration:

1. Tạo Room database với dữ liệu test
2. Reset migration status: `resetMigrationStatus()`
3. Gọi `forceMigration(context)`
4. Kiểm tra dữ liệu trong PowerSync database
