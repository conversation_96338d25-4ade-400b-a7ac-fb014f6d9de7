package com.amobilab.ezmath.ai.data.db

import android.app.Application
import android.content.Context
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import amobi.module.common.utils.debugLog

/**
 * Ví dụ về cách sử dụng Migration từ Room sang PowerSync
 */

// Ví dụ 1: Trong Application class
class MyApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Khởi tạo database và tự động migration
        initializeDatabaseWithMigration()
    }
    
    private fun initializeDatabaseWithMigration() {
        // Sử dụng GlobalScope hoặc ApplicationScope
        kotlinx.coroutines.GlobalScope.launch {
            try {
                // Khởi tạo database - migration sẽ tự động chạy
                AppDatabase.getInstance().init(this@MyApplication)
                debugLog("Database initialization completed with auto migration")
            } catch (e: Exception) {
                debugLog("Database initialization failed: ${e.message}")
                e.printStackTrace()
            }
        }
    }
}

// Ví dụ 2: Trong MainActivity
class MainActivity : androidx.activity.ComponentActivity() {
    
    override fun onCreate(savedInstanceState: android.os.Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Khởi tạo database với migration
        initializeDatabase()
    }
    
    private fun initializeDatabase() {
        lifecycleScope.launch {
            try {
                // Tự động migration
                AppDatabase.getInstance().init(this@MainActivity)
                debugLog("Database initialized successfully")
                
                // Bây giờ có thể sử dụng database
                val historyRepo = AppDatabase.getInstance().getHistoryRepository()
                val chatRepo = AppDatabase.getInstance().getChatRepository()
                val coinRepo = AppDatabase.getInstance().getCoinHistoryRepository()
                
            } catch (e: Exception) {
                debugLog("Database initialization failed: ${e.message}")
                handleDatabaseError(e)
            }
        }
    }
    
    private fun handleDatabaseError(error: Exception) {
        // Xử lý lỗi database
        // Có thể hiển thị dialog hoặc retry
    }
}

// Ví dụ 3: Migration thủ công với UI feedback
class SettingsActivity : androidx.activity.ComponentActivity() {
    
    private fun performManualMigration() {
        lifecycleScope.launch {
            try {
                // Hiển thị loading
                showMigrationProgress(true)
                
                // Thực hiện migration thủ công
                val success = AppDatabase.getInstance().forceMigration(this@SettingsActivity)
                
                if (success) {
                    showMigrationResult("Migration thành công!")
                } else {
                    showMigrationResult("Migration thất bại. Vui lòng thử lại.")
                }
                
            } catch (e: Exception) {
                showMigrationResult("Lỗi migration: ${e.message}")
            } finally {
                showMigrationProgress(false)
            }
        }
    }
    
    private fun showMigrationProgress(show: Boolean) {
        // Hiển thị/ẩn progress bar
    }
    
    private fun showMigrationResult(message: String) {
        // Hiển thị kết quả migration
    }
}

// Ví dụ 4: Kiểm tra trạng thái migration
class DatabaseUtils {
    
    companion object {
        
        /**
         * Kiểm tra xem migration đã được thực hiện chưa
         */
        fun isMigrationCompleted(): Boolean {
            return amobi.module.common.configs.PrefAssist.getBoolean(
                "room_to_powersync_migration_completed", 
                false
            )
        }
        
        /**
         * Reset migration status (chỉ dùng cho testing)
         */
        fun resetMigrationForTesting() {
            AppDatabase.getInstance().resetMigrationStatus()
        }
        
        /**
         * Khởi tạo database an toàn với error handling
         */
        suspend fun safeInitializeDatabase(context: Context): Boolean {
            return try {
                AppDatabase.getInstance().init(context)
                true
            } catch (e: Exception) {
                debugLog("Safe database initialization failed: ${e.message}")
                false
            }
        }
    }
}

// Ví dụ 5: Testing Migration
class MigrationTest {
    
    suspend fun testMigration(context: Context) {
        // 1. Reset migration status
        AppDatabase.getInstance().resetMigrationStatus()
        
        // 2. Tạo Room database với dữ liệu test (nếu cần)
        // createTestRoomData(context)
        
        // 3. Thực hiện migration
        val success = AppDatabase.getInstance().forceMigration(context)
        
        // 4. Kiểm tra kết quả
        if (success) {
            debugLog("Migration test passed")
            // Kiểm tra dữ liệu trong PowerSync
            verifyMigratedData()
        } else {
            debugLog("Migration test failed")
        }
    }
    
    private suspend fun verifyMigratedData() {
        val historyRepo = AppDatabase.getInstance().getHistoryRepository()
        val chatRepo = AppDatabase.getInstance().getChatRepository()
        val coinRepo = AppDatabase.getInstance().getCoinHistoryRepository()
        
        // Kiểm tra dữ liệu đã được migrate đúng chưa
        val histories = historyRepo.getAllHistory()
        val coinHistories = coinRepo.getAllTransactions()
        
        debugLog("Migrated ${histories.size} histories and ${coinHistories.size} coin histories")
    }
}

// Ví dụ 6: Sử dụng trong ViewModel
class MainViewModel : androidx.lifecycle.ViewModel() {
    
    private val _migrationStatus = androidx.lifecycle.MutableLiveData<MigrationStatus>()
    val migrationStatus: androidx.lifecycle.LiveData<MigrationStatus> = _migrationStatus
    
    fun initializeDatabase(context: Context) {
        viewModelScope.launch {
            _migrationStatus.value = MigrationStatus.InProgress
            
            try {
                AppDatabase.getInstance().init(context)
                _migrationStatus.value = MigrationStatus.Success
            } catch (e: Exception) {
                _migrationStatus.value = MigrationStatus.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    sealed class MigrationStatus {
        object InProgress : MigrationStatus()
        object Success : MigrationStatus()
        data class Error(val message: String) : MigrationStatus()
    }
}
